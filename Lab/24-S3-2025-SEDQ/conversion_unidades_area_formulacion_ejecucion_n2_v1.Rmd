---
output:
  pdf_document:
    keep_tex: true
    extra_dependencies: ["graphicx", "float", "tikz", "xcolor"]
  word_document: default
  html_document: default
icfes:
  competencia: formulacion_ejecucion
  nivel_dificultad: 2
  contenido:
    categoria: geometria
    tipo: generico
  contexto: familiar
  eje_axial: eje2
  componente: numerico_variacional
---

```{r setup, include=FALSE}
# Configuración para todos los formatos de salida
Sys.setlocale(category = "LC_NUMERIC", locale = "C")
options(OutDec = ".")

# Configurar el motor LaTeX globalmente
options(tikzLatex = "pdflatex")
options(tikzXelatex = FALSE)
options(tikzLatexPackages = c(
  "\\usepackage{tikz}",
  "\\usepackage{colortbl}",
  "\\usepackage{xcolor}"
))

library(exams)
library(reticulate)
library(digest)
library(testthat)

typ <- match_exams_device()
options(scipen = 999)
knitr::opts_chunk$set(
  warning = FALSE,
  message = FALSE,
  fig.showtext = FALSE,
  fig.cap = "",
  fig.keep = 'all',
  dev = c("png", "pdf"),
  dpi = 150
)

# Configuración para chunks de Python
knitr::knit_engines$set(python = function(options) {
  knitr::engine_output(options, options$code, '')
})

# Asegurar que Python esté correctamente configurado
use_python(Sys.which("python"), required = TRUE)
```

```{r data_generation, echo=FALSE, results="hide"}
options(OutDec = ".")

# Establecer semilla aleatoria para diversidad
#set.seed(sample(1:100000, 1))

# Función principal de generación de datos
generar_datos <- function() {
  # Aleatorizar contextos de objetos rectangulares
  objetos <- c("espejo", "cuadro", "tapete", "cartel", "póster", "tablero", "panel")
  objeto <- sample(objetos, 1)
  
  # Aleatorizar ubicaciones
  ubicaciones <- c("sala", "dormitorio", "oficina", "aula", "cocina", "baño",
                   "recibidor", "estudio", "comedor", "habitación")
  ubicacion <- sample(ubicaciones, 1)
  
  # Aleatorizar personas
  personas <- c("una persona", "María", "Juan", "Ana", "Carlos", "Sofía", 
                "Pedro", "Laura", "Diego", "Carmen", "Luis", "Elena")
  persona <- sample(personas, 1)
  
  # Generar dimensiones realistas en cm (múltiplos de 5 para facilitar cálculos)
  ancho_cm <- sample(seq(60, 200, 5), 1)  # Entre 60 y 200 cm
  largo_cm <- sample(seq(80, 250, 5), 1)  # Entre 80 y 250 cm
  
  # Calcular área en cm²
  area_cm2 <- ancho_cm * largo_cm
  
  # Conversión correcta a m² (dividir por 10,000)
  area_m2_correcta <- area_cm2 / 10000
  
  # Generar distractores comunes
  # Error 1: Dividir por 100 (confundir con conversión lineal)
  distractor_1 <- area_cm2 / 100
  
  # Error 2: Dividir por 1000 
  distractor_2 <- area_cm2 / 1000
  
  # Error 3: Multiplicar por 0.01 (error conceptual)
  distractor_3 <- area_cm2 * 0.01
  
  # Error 4: Usar solo las dimensiones sin el área
  distractor_4 <- (ancho_cm + largo_cm) / 100
  
  # Error 5: Dividir por 10000 pero usar solo una dimensión
  distractor_5 <- ancho_cm / 10000
  
  # Crear pool de distractores
  distractores_pool <- c(distractor_1, distractor_2, distractor_3, 
                        distractor_4, distractor_5)
  
  # Seleccionar 3 distractores únicos diferentes de la respuesta correcta
  distractores_seleccionados <- sample(distractores_pool[
    distractores_pool != area_m2_correcta], 3)
  
  # Crear opciones (respuesta correcta + 3 distractores)
  opciones_valores <- c(area_m2_correcta, distractores_seleccionados)
  
  # Aleatorizar orden de las opciones
  orden <- sample(1:4)
  opciones_ordenadas <- opciones_valores[orden]
  
  # Encontrar posición de la respuesta correcta
  posicion_correcta <- which(opciones_ordenadas == area_m2_correcta)
  
  # Crear vector de solución
  solucion <- integer(4)
  solucion[posicion_correcta] <- 1
  
  return(list(
    objeto = objeto,
    ubicacion = ubicacion,
    persona = persona,
    ancho_cm = ancho_cm,
    largo_cm = largo_cm,
    area_cm2 = area_cm2,
    area_m2_correcta = area_m2_correcta,
    opciones = opciones_ordenadas,
    solucion = solucion,
    posicion_correcta = posicion_correcta
  ))
}

# Generar datos del ejercicio
datos <- generar_datos()

# Extraer variables individuales
objeto <- datos$objeto
ubicacion <- datos$ubicacion
persona <- datos$persona
ancho_cm <- datos$ancho_cm
largo_cm <- datos$largo_cm
area_cm2 <- datos$area_cm2
area_m2_correcta <- datos$area_m2_correcta
opciones <- datos$opciones
solucion <- datos$solucion
posicion_correcta <- datos$posicion_correcta
```

```{r version_diversity_test, echo=FALSE, results="hide"}
# Prueba obligatoria de diversidad de versiones
test_that("Prueba de diversidad de versiones", {
  versiones <- list()
  for(i in 1:1000) {
    datos_test <- generar_datos()
    versiones[[i]] <- digest::digest(datos_test)
  }
  
  n_versiones_unicas <- length(unique(versiones))
  expect_true(n_versiones_unicas >= 300,
              info = paste("Solo se generaron", n_versiones_unicas,
                          "versiones únicas. Se requieren al menos 300."))
})
```

```{r generar_diagrama_python, echo=FALSE, results="hide"}
# Código Python para crear diagrama del objeto rectangular
codigo_python_diagrama <- sprintf("
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import random

# Datos del problema
ancho_cm = %d
largo_cm = %d
area_cm2 = %d
objeto = '%s'

# Configurar figura
fig, ax = plt.subplots(figsize=(8, 6))

# Colores aleatorios
colores = ['lightblue', 'lightgreen', 'lightcoral', 'lightyellow', 'lightpink', 'lightgray']
color_objeto = random.choice(colores)

# Crear rectángulo proporcional (escalar para visualización)
escala = min(6/max(ancho_cm, largo_cm), 4/min(ancho_cm, largo_cm))
ancho_visual = ancho_cm * escala * 0.01
largo_visual = largo_cm * escala * 0.01

# Dibujar rectángulo
rectangulo = patches.Rectangle((1, 1), ancho_visual, largo_visual, 
                              linewidth=2, edgecolor='black', 
                              facecolor=color_objeto, alpha=0.7)
ax.add_patch(rectangulo)

# Añadir dimensiones
ax.annotate(f'{ancho_cm} cm', xy=(1 + ancho_visual/2, 0.8), 
           ha='center', va='top', fontsize=12, fontweight='bold')
ax.annotate(f'{largo_cm} cm', xy=(0.8, 1 + largo_visual/2), 
           ha='right', va='center', fontsize=12, fontweight='bold', rotation=90)

# Añadir área
ax.text(1 + ancho_visual/2, 1 + largo_visual/2, 
        f'Área = {area_cm2:,} cm²', 
        ha='center', va='center', fontsize=14, fontweight='bold',
        bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))

# Configurar ejes
ax.set_xlim(0, 3)
ax.set_ylim(0, 3)
ax.set_aspect('equal')
ax.axis('off')

# Título
ax.set_title(f'{objeto.title()} rectangular', fontsize=16, fontweight='bold', pad=20)

# Guardar en múltiples formatos para compatibilidad con R-exams
plt.tight_layout()
plt.savefig('diagrama_objeto.png', dpi=150, bbox_inches='tight')
plt.savefig('diagrama_objeto.pdf', dpi=150, bbox_inches='tight')
plt.close()
", ancho_cm, largo_cm, area_cm2, objeto)

# Ejecutar código Python
py_run_string(codigo_python_diagrama)
```

Question
========

Después de haber comprado un `r objeto` rectangular, `r persona` desea ubicarlo en la pared de su `r ubicacion`. El `r objeto` tiene un área de `r format(area_cm2, big.mark = ",")` cm$^2$ y dimensiones de `r ancho_cm` cm de ancho y `r largo_cm` cm de largo.

```{r mostrar_diagrama, echo=FALSE, results='asis', fig.align='center'}
# Usando método de ejemplos funcionales para incluir imágenes
cat("![](diagrama_objeto.png){width=70%}")
```

Teniendo en cuenta que 1 m equivale a 100 cm, ¿cuál es el área que ocupa el `r objeto` en m$^2$?

Answerlist
----------
- `r opciones[1]` m$^2$
- `r opciones[2]` m$^2$
- `r opciones[3]` m$^2$
- `r opciones[4]` m$^2$

Solution
========

Para convertir el área de cm$^2$ a m$^2$, debemos recordar que:

- 1 m = 100 cm
- Por lo tanto: 1 m$^2$ = (100 cm)$^2$ = 10,000 cm$^2$

**Proceso de solución:**

1. **Área dada:** `r format(area_cm2, big.mark = ",")` cm$^2$

2. **Factor de conversión:** 1 m$^2$ = 10,000 cm$^2$

3. **Conversión:** 
   $$\text{Área en m}^2 = \frac{\text{Área en cm}^2}{10,000} = \frac{`r format(area_cm2, big.mark = ",")`}{10,000} = `r area_m2_correcta` \text{ m}^2$$

**Verificación con las dimensiones:**
- Ancho: `r ancho_cm` cm = `r ancho_cm/100` m
- Largo: `r largo_cm` cm = `r largo_cm/100` m  
- Área: `r ancho_cm/100` m × `r largo_cm/100` m = `r area_m2_correcta` m$^2$ (correcto)

Answerlist
----------
- `r if(solucion[1] == 1) "Verdadero. Esta es la conversión correcta aplicando el factor 1 m² = 10,000 cm²." else "Falso. Este valor no corresponde a la conversión correcta."`
- `r if(solucion[2] == 1) "Verdadero. Esta es la conversión correcta aplicando el factor 1 m² = 10,000 cm²." else "Falso. Este valor no corresponde a la conversión correcta."`
- `r if(solucion[3] == 1) "Verdadero. Esta es la conversión correcta aplicando el factor 1 m² = 10,000 cm²." else "Falso. Este valor no corresponde a la conversión correcta."`
- `r if(solucion[4] == 1) "Verdadero. Esta es la conversión correcta aplicando el factor 1 m² = 10,000 cm²." else "Falso. Este valor no corresponde a la conversión correcta."`

Meta-information
================
exname: conversion_area_cm2_m2
extype: schoice
exsolution: `r paste(as.integer(solucion), collapse="")`
exshuffle: TRUE
exsection: Conversión de unidades de área
