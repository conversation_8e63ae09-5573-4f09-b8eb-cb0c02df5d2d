% Options for packages loaded elsewhere
\PassOptionsToPackage{unicode}{hyperref}
\PassOptionsToPackage{hyphens}{url}
%
\documentclass[
]{article}
\usepackage{amsmath,amssymb}
\usepackage{iftex}
\ifPDFTeX
  \usepackage[T1]{fontenc}
  \usepackage[utf8]{inputenc}
  \usepackage{textcomp} % provide euro and other symbols
\else % if luatex or xetex
  \usepackage{unicode-math} % this also loads fontspec
  \defaultfontfeatures{Scale=MatchLowercase}
  \defaultfontfeatures[\rmfamily]{Ligatures=TeX,Scale=1}
\fi
\usepackage{lmodern}
\ifPDFTeX\else
  % xetex/luatex font selection
\fi
% Use upquote if available, for straight quotes in verbatim environments
\IfFileExists{upquote.sty}{\usepackage{upquote}}{}
\IfFileExists{microtype.sty}{% use microtype if available
  \usepackage[]{microtype}
  \UseMicrotypeSet[protrusion]{basicmath} % disable protrusion for tt fonts
}{}
\makeatletter
\@ifundefined{KOMAClassName}{% if non-KOMA class
  \IfFileExists{parskip.sty}{%
    \usepackage{parskip}
  }{% else
    \setlength{\parindent}{0pt}
    \setlength{\parskip}{6pt plus 2pt minus 1pt}}
}{% if KOMA class
  \KOMAoptions{parskip=half}}
\makeatother
\usepackage{xcolor}
\usepackage[margin=1in]{geometry}
\usepackage{graphicx}
\makeatletter
\def\maxwidth{\ifdim\Gin@nat@width>\linewidth\linewidth\else\Gin@nat@width\fi}
\def\maxheight{\ifdim\Gin@nat@height>\textheight\textheight\else\Gin@nat@height\fi}
\makeatother
% Scale images if necessary, so that they will not overflow the page
% margins by default, and it is still possible to overwrite the defaults
% using explicit options in \includegraphics[width, height, ...]{}
\setkeys{Gin}{width=\maxwidth,height=\maxheight,keepaspectratio}
% Set default figure placement to htbp
\makeatletter
\def\fps@figure{htbp}
\makeatother
\setlength{\emergencystretch}{3em} % prevent overfull lines
\providecommand{\tightlist}{%
  \setlength{\itemsep}{0pt}\setlength{\parskip}{0pt}}
\setcounter{secnumdepth}{-\maxdimen} % remove section numbering
\usepackage{graphicx}
\usepackage{float}
\usepackage{tikz}
\usepackage{xcolor}
\ifLuaTeX
  \usepackage{selnolig}  % disable illegal ligatures
\fi
\usepackage{bookmark}
\IfFileExists{xurl.sty}{\usepackage{xurl}}{} % add URL line breaks if available
\urlstyle{same}
\hypersetup{
  hidelinks,
  pdfcreator={LaTeX via pandoc}}

\author{}
\date{\vspace{-2.5em}}

\begin{document}

\section{Question}\label{question}

Después de haber comprado un ventana rectangular, Luis desea ubicarlo en
la pared de su cocina. El ventana tiene un área de 16,650 cm\(^2\) y
dimensiones de 90 cm de ancho y 185 cm de largo.

\includegraphics{diagrama_objeto.png}

Teniendo en cuenta que 1 m equivale a 100 cm, ¿cuál es el área que ocupa
el ventana en m\(^2\)?

\subsection{Answerlist}\label{answerlist}

\begin{itemize}
\tightlist
\item
  0,009 m\(^2\)
\item
  166,5 m\(^2\)
\item
  16,65 m\(^2\)
\item
  1,665 m\(^2\)
\end{itemize}

\section{Solution}\label{solution}

Para convertir el área de cm\(^2\) a m\(^2\), debemos recordar que:

\begin{itemize}
\tightlist
\item
  1 m = 100 cm
\item
  Por lo tanto: 1 m\(^2\) = (100 cm)\(^2\) = 10,000 cm\(^2\)
\end{itemize}

\textbf{Proceso de solución:}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Área dada:} 16,650 cm\(^2\)
\item
  \textbf{Factor de conversión:} 1 m\(^2\) = 10,000 cm\(^2\)
\item
  \textbf{Conversión:}
  \[\text{Área en m}^2 = \frac{\text{Área en cm}^2}{10,000} = \frac{16,650}{10,000} = 1,665 \text{ m}^2\]
\end{enumerate}

\textbf{Verificación con las dimensiones:} - Ancho: 90 cm = 0,9 m -
Largo: 185 cm = 1,85 m\\
- Área: 0,9 m × 1,85 m = 1,665 m\(^2\) (correcto)

\subsection{Answerlist}\label{answerlist-1}

\begin{itemize}
\tightlist
\item
  Falso. Este valor no corresponde a la conversión correcta.
\item
  Falso. Este valor no corresponde a la conversión correcta.
\item
  Falso. Este valor no corresponde a la conversión correcta.
\item
  Verdadero. Esta es la conversión correcta aplicando el factor 1 m² =
  10,000 cm².
\end{itemize}

\section{Meta-information}\label{meta-information}

exname: conversion\_area\_cm2\_m2 extype: schoice exsolution: 0001
exshuffle: TRUE exsection: Conversión de unidades de área

\end{document}
