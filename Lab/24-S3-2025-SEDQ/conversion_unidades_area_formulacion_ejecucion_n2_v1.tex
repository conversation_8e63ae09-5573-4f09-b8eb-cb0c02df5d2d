% Options for packages loaded elsewhere
\PassOptionsToPackage{unicode}{hyperref}
\PassOptionsToPackage{hyphens}{url}
%
\documentclass[
]{article}
\usepackage{amsmath,amssymb}
\usepackage{iftex}
\ifPDFTeX
  \usepackage[T1]{fontenc}
  \usepackage[utf8]{inputenc}
  \usepackage{textcomp} % provide euro and other symbols
\else % if luatex or xetex
  \usepackage{unicode-math} % this also loads fontspec
  \defaultfontfeatures{Scale=MatchLowercase}
  \defaultfontfeatures[\rmfamily]{Ligatures=TeX,Scale=1}
\fi
\usepackage{lmodern}
\ifPDFTeX\else
  % xetex/luatex font selection
\fi
% Use upquote if available, for straight quotes in verbatim environments
\IfFileExists{upquote.sty}{\usepackage{upquote}}{}
\IfFileExists{microtype.sty}{% use microtype if available
  \usepackage[]{microtype}
  \UseMicrotypeSet[protrusion]{basicmath} % disable protrusion for tt fonts
}{}
\makeatletter
\@ifundefined{KOMAClassName}{% if non-KOMA class
  \IfFileExists{parskip.sty}{%
    \usepackage{parskip}
  }{% else
    \setlength{\parindent}{0pt}
    \setlength{\parskip}{6pt plus 2pt minus 1pt}}
}{% if KOMA class
  \KOMAoptions{parskip=half}}
\makeatother
\usepackage{xcolor}
\usepackage[margin=1in]{geometry}
\usepackage{graphicx}
\makeatletter
\newsavebox\pandoc@box
\newcommand*\pandocbounded[1]{% scales image to fit in text height/width
  \sbox\pandoc@box{#1}%
  \Gscale@div\@tempa{\textheight}{\dimexpr\ht\pandoc@box+\dp\pandoc@box\relax}%
  \Gscale@div\@tempb{\linewidth}{\wd\pandoc@box}%
  \ifdim\@tempb\p@<\@tempa\p@\let\@tempa\@tempb\fi% select the smaller of both
  \ifdim\@tempa\p@<\p@\scalebox{\@tempa}{\usebox\pandoc@box}%
  \else\usebox{\pandoc@box}%
  \fi%
}
% Set default figure placement to htbp
\def\fps@figure{htbp}
\makeatother
\setlength{\emergencystretch}{3em} % prevent overfull lines
\providecommand{\tightlist}{%
  \setlength{\itemsep}{0pt}\setlength{\parskip}{0pt}}
\setcounter{secnumdepth}{-\maxdimen} % remove section numbering
\usepackage{graphicx}
\usepackage{float}
\usepackage{tikz}
\usepackage{xcolor}
\usepackage{bookmark}
\IfFileExists{xurl.sty}{\usepackage{xurl}}{} % add URL line breaks if available
\urlstyle{same}
\hypersetup{
  hidelinks,
  pdfcreator={LaTeX via pandoc}}

\author{}
\date{\vspace{-2.5em}}

\begin{document}

\section{Question}\label{question}

Después de haber comprado un tablero rectangular, Luis desea ubicarlo en
la pared de su oficina. El tablero tiene un área de 7,200 cm\(^2\) y
dimensiones de 60 cm de ancho y 120 cm de largo.

\includegraphics[width=0.7\linewidth,height=\textheight,keepaspectratio]{diagrama_objeto.png}

Teniendo en cuenta que 1 m equivale a 100 cm, ¿cuál es el área que ocupa
el tablero en m\(^2\)?

\subsection{Answerlist}\label{answerlist}

\begin{itemize}
\tightlist
\item
  0,72 m\(^2\)
\item
  72 m\(^2\)
\item
  7,2 m\(^2\)
\item
  0,006 m\(^2\)
\end{itemize}

\section{Solution}\label{solution}

Para convertir el área de cm\(^2\) a m\(^2\), debemos recordar que:

\begin{itemize}
\tightlist
\item
  1 m = 100 cm
\item
  Por lo tanto: 1 m\(^2\) = (100 cm)\(^2\) = 10,000 cm\(^2\)
\end{itemize}

\textbf{Proceso de solución:}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Área dada:} 7,200 cm\(^2\)
\item
  \textbf{Factor de conversión:} 1 m\(^2\) = 10,000 cm\(^2\)
\item
  \textbf{Conversión:}
  \[\text{Área en m}^2 = \frac{\text{Área en cm}^2}{10,000} = \frac{7,200}{10,000} = 0,72 \text{ m}^2\]
\end{enumerate}

\textbf{Verificación con las dimensiones:} - Ancho: 60 cm = 0,6 m -
Largo: 120 cm = 1,2 m\\
- Área: 0,6 m × 1,2 m = 0,72 m\(^2\) (correcto)

\subsection{Answerlist}\label{answerlist-1}

\begin{itemize}
\tightlist
\item
  Verdadero. Esta es la conversión correcta aplicando el factor 1 m² =
  10,000 cm².
\item
  Falso. Este valor no corresponde a la conversión correcta.
\item
  Falso. Este valor no corresponde a la conversión correcta.
\item
  Falso. Este valor no corresponde a la conversión correcta.
\end{itemize}

\section{Meta-information}\label{meta-information}

exname: conversion\_area\_cm2\_m2 extype: schoice exsolution: 1000
exshuffle: TRUE exsection: Conversión de unidades de área

\end{document}
